/**
 * 重构后代码测试脚本
 * 用于验证模块化重构是否成功
 */

// 测试模块导入
function testModuleImports() {
  console.log("=== 测试模块导入 ===");
  
  try {
    const { SCALE_CONFIGS, TIME_CONSTANTS, UI_CONSTANTS } = require('./modules/constants.js');
    console.log("✅ constants.js 导入成功");
    console.log("SCALE_CONFIGS:", SCALE_CONFIGS);
    console.log("TIME_CONSTANTS:", TIME_CONSTANTS);
    
    const { TimeUtils } = require('./modules/timeUtils.js');
    console.log("✅ timeUtils.js 导入成功");
    console.log("TimeUtils.formatTime:", typeof TimeUtils.formatTime);
    
    const { ScaleManager } = require('./modules/scaleManager.js');
    console.log("✅ scaleManager.js 导入成功");
    console.log("ScaleManager:", typeof ScaleManager);
    
    const { ZoomManager } = require('./modules/zoomManager.js');
    console.log("✅ zoomManager.js 导入成功");
    console.log("ZoomManager:", typeof ZoomManager);
    
    const { PlaybackManager } = require('./modules/playbackManager.js');
    console.log("✅ playbackManager.js 导入成功");
    console.log("PlaybackManager:", typeof PlaybackManager);
    
    return true;
  } catch (error) {
    console.error("❌ 模块导入失败:", error);
    return false;
  }
}

// 测试时间工具
function testTimeUtils() {
  console.log("\n=== 测试时间工具 ===");
  
  try {
    const { TimeUtils } = require('./modules/timeUtils.js');
    
    const now = Date.now();
    const formatted = TimeUtils.formatTime(now);
    console.log("✅ formatTime 测试:", formatted);
    
    const duration = TimeUtils.formatDuration(65000);
    console.log("✅ formatDuration 测试:", duration);
    
    const aligned = TimeUtils.alignToInterval(now, 60000);
    console.log("✅ alignToInterval 测试:", aligned);
    
    const isFuture = TimeUtils.isFutureTime(now + 10000);
    console.log("✅ isFutureTime 测试:", isFuture);
    
    return true;
  } catch (error) {
    console.error("❌ 时间工具测试失败:", error);
    return false;
  }
}

// 测试管理器初始化
function testManagerInitialization() {
  console.log("\n=== 测试管理器初始化 ===");
  
  try {
    const { ScaleManager } = require('./modules/scaleManager.js');
    const { ZoomManager } = require('./modules/zoomManager.js');
    const { PlaybackManager } = require('./modules/playbackManager.js');
    
    // 模拟页面上下文
    const mockContext = {
      data: {
        scaleLevel: 1,
        scaleUnit: "minute",
        currentTime: Date.now(),
        baseTime: Date.now(),
        pixelsPerSecond: 10,
        containerWidth: 375,
        isRecording: false,
        canZoomIn: true,
        canZoomOut: true,
      },
      setData: function(data) {
        Object.assign(this.data, data);
        console.log("setData 调用:", data);
      },
      _updateRecordArea: function() {
        console.log("_updateRecordArea 调用");
      }
    };
    
    const scaleManager = new ScaleManager(mockContext);
    console.log("✅ ScaleManager 初始化成功");
    
    const zoomManager = new ZoomManager(mockContext, scaleManager);
    console.log("✅ ZoomManager 初始化成功");
    
    const playbackManager = new PlaybackManager(mockContext);
    console.log("✅ PlaybackManager 初始化成功");
    
    return { scaleManager, zoomManager, playbackManager, mockContext };
  } catch (error) {
    console.error("❌ 管理器初始化失败:", error);
    return null;
  }
}

// 测试刻度生成
function testScaleGeneration(managers) {
  console.log("\n=== 测试刻度生成 ===");
  
  if (!managers) {
    console.error("❌ 管理器未初始化");
    return false;
  }
  
  try {
    const { scaleManager } = managers;
    
    // 测试刻度配置获取
    const config = scaleManager.getScaleConfig("minute");
    console.log("✅ 刻度配置获取:", config);
    
    // 测试刻度信息获取
    const date = new Date();
    const info = scaleManager.getScaleInfo(date, "minute");
    console.log("✅ 刻度信息获取:", info);
    
    // 测试同步刻度生成
    scaleManager.generateTimeScales();
    console.log("✅ 同步刻度生成完成");
    
    return true;
  } catch (error) {
    console.error("❌ 刻度生成测试失败:", error);
    return false;
  }
}

// 测试缩放功能
function testZoomFunctionality(managers) {
  console.log("\n=== 测试缩放功能 ===");
  
  if (!managers) {
    console.error("❌ 管理器未初始化");
    return false;
  }
  
  try {
    const { zoomManager, mockContext } = managers;
    
    // 测试缩放按钮状态更新
    zoomManager.updateZoomButtonStatus();
    console.log("✅ 缩放按钮状态更新");
    
    // 测试录制参数计算
    const newConfig = { unit: "second", text: "秒", interval: 1000 };
    const params = zoomManager.calculateRecordingParams(newConfig);
    console.log("✅ 录制参数计算:", params);
    
    // 测试时间参数计算
    const timeParams = zoomManager.calculateTimeParams(newConfig);
    console.log("✅ 时间参数计算:", timeParams);
    
    return true;
  } catch (error) {
    console.error("❌ 缩放功能测试失败:", error);
    return false;
  }
}

// 测试播放控制
function testPlaybackControl(managers) {
  console.log("\n=== 测试播放控制 ===");
  
  if (!managers) {
    console.error("❌ 管理器未初始化");
    return false;
  }
  
  try {
    const { playbackManager, mockContext } = managers;
    
    // 模拟必要的方法
    mockContext._timeToSliderValue = function(time) {
      return 50; // 模拟返回值
    };
    mockContext.generateTimeScales = function() {
      console.log("generateTimeScales 调用");
    };
    mockContext._updateVideoUrlAfterDrag = async function(time, keepState) {
      console.log("_updateVideoUrlAfterDrag 调用:", time, keepState);
    };
    
    // 测试速度计算
    playbackManager.velocityTracker = {
      positions: [
        { x: 100, time: Date.now() - 100 },
        { x: 150, time: Date.now() }
      ]
    };
    const velocity = playbackManager.calculateVelocity();
    console.log("✅ 速度计算:", velocity);
    
    // 测试拖拽状态清理
    playbackManager.cleanupDragState();
    console.log("✅ 拖拽状态清理");
    
    return true;
  } catch (error) {
    console.error("❌ 播放控制测试失败:", error);
    return false;
  }
}

// 运行所有测试
function runAllTests() {
  console.log("开始重构代码测试...\n");
  
  const results = [];
  
  results.push(testModuleImports());
  results.push(testTimeUtils());
  
  const managers = testManagerInitialization();
  results.push(!!managers);
  
  if (managers) {
    results.push(testScaleGeneration(managers));
    results.push(testZoomFunctionality(managers));
    results.push(testPlaybackControl(managers));
  }
  
  const passedTests = results.filter(r => r).length;
  const totalTests = results.length;
  
  console.log(`\n=== 测试结果 ===`);
  console.log(`通过: ${passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log("🎉 所有测试通过！重构成功！");
  } else {
    console.log("⚠️ 部分测试失败，需要修复");
  }
  
  return passedTests === totalTests;
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testModuleImports,
    testTimeUtils,
    testManagerInitialization,
    testScaleGeneration,
    testZoomFunctionality,
    testPlaybackControl,
    runAllTests
  };
} else {
  // 在浏览器环境中直接暴露到全局
  window.refactorTests = {
    testModuleImports,
    testTimeUtils,
    testManagerInitialization,
    testScaleGeneration,
    testZoomFunctionality,
    testPlaybackControl,
    runAllTests
  };
  
  console.log("重构测试工具已加载，可以使用:");
  console.log("- refactorTests.runAllTests() // 运行所有测试");
}
