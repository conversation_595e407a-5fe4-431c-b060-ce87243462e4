/**
 * 播放控制管理模块
 * 负责视频播放、暂停、拖拽等操作
 */

import { TimeUtils } from './timeUtils.js';
import { ANIMATION_CONSTANTS } from './constants.js';

export class PlaybackManager {
  constructor(context) {
    this.context = context;
    this.timer = null;
    this.velocityTracker = null;
    this.inertiaAnimationId = null;
  }

  /**
   * 开始自动播放
   */
  startAutoPlay() {
    this.stopAutoPlay();
    
    const data = this.context.data;
    const startTime = data.pausedTime || data.currentTime || TimeUtils.getCurrentTime();
    
    this.timer = setInterval(() => {
      this.updatePlaybackTime();
    }, ANIMATION_CONSTANTS.FRAME_RATE_60);
    
    this.context.setData({
      isPlaying: true,
      playStartTime: TimeUtils.getCurrentTime(),
      playStartCenterTime: startTime,
    });
  }

  /**
   * 停止自动播放
   */
  stopAutoPlay() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    
    this.context.setData({
      isPlaying: false,
      pausedTime: this.context.data.currentTime,
    });
  }

  /**
   * 更新播放时间
   */
  updatePlaybackTime() {
    const data = this.context.data;
    const now = TimeUtils.getCurrentTime();
    const realCurrentTime = now;
    
    if (!data.playStartTime || !data.playStartCenterTime) {
      return;
    }
    
    const elapsed = now - data.playStartTime;
    const newCurrentTime = data.playStartCenterTime + elapsed;
    
    if (newCurrentTime > realCurrentTime) {
      this.handleRealtimeSync(realCurrentTime);
      return;
    }
    
    this.context.setData({
      currentTime: newCurrentTime,
      baseTime: newCurrentTime,
      formattedCurrentTime: TimeUtils.formatTime(newCurrentTime),
    });
    
    this.context.generateTimeScales();
    
    if (data.isRecording) {
      this.context._updateRecordAreaNew && this.context._updateRecordAreaNew();
    }
  }

  /**
   * 处理实时同步
   */
  handleRealtimeSync(realCurrentTime) {
    const sliderValue = this.context._timeToSliderValue(realCurrentTime);
    
    this.context.setData({
      currentTime: realCurrentTime,
      baseTime: realCurrentTime,
      pausedTime: 0,
      sliderValue: sliderValue,
      isPlaying: true,
      formattedCurrentTime: TimeUtils.formatTime(realCurrentTime),
    });
    
    this.context.generateTimeScales();
    this.startAutoPlay();
  }

  /**
   * 切换播放状态
   */
  togglePlay() {
    if (this.context.data.isDragging || this.context.data.isRecordAreaDragging) {
      console.log("togglePlay: 正在拖拽，忽略播放切换");
      return;
    }

    const isPlaying = !this.context.data.isPlaying;

    console.log("togglePlay: 切换播放状态", {
      from: this.context.data.isPlaying,
      to: isPlaying,
      currentTime: this.context.data.currentTime,
      pausedTime: this.context.data.pausedTime
    });

    if (!isPlaying) {
      this.stopAutoPlay();
    } else {
      this.startAutoPlay();
    }
  }

  /**
   * 跳转到指定时间
   */
  async jumpToTime(targetTime, shouldAutoPlay = true) {
    this.stopAutoPlay();
    
    const sliderValue = this.context._timeToSliderValue(targetTime);
    
    this.context.setData({
      isPlaying: false,
      baseTime: targetTime,
      currentTime: targetTime,
      pausedTime: targetTime,
      sliderValue: sliderValue,
      formattedCurrentTime: TimeUtils.formatTime(targetTime),
      isDragging: false,
      isRecordAreaDragging: false,
    });
    
    this.context.generateTimeScales();
    
    if (this.context.data.isRecording) {
      setTimeout(() => {
        this.context._updateRecordAreaNew && this.context._updateRecordAreaNew();
      }, 10);
    }
    
    await this.context._updateVideoUrlAfterDrag(targetTime, true);
    
    if (shouldAutoPlay) {
      await this.delayedAutoPlay(targetTime);
    }
  }

  /**
   * 延迟自动播放
   */
  async delayedAutoPlay(targetTime) {
    return new Promise((resolve) => {
      wx.nextTick(() => {
        setTimeout(() => {
          const currentTime = this.context.data.currentTime;
          const timeDiff = Math.abs(currentTime - targetTime);
          
          if (timeDiff < 1000 && !this.context.data.isDragging) {
            console.log("启动自动播放", { currentTime, targetTime, timeDiff });
            
            this.context.setData({ isPlaying: true });
            this.startAutoPlay();
          } else {
            console.warn("跳过自动播放", {
              currentTime, targetTime, timeDiff,
              isDragging: this.context.data.isDragging
            });
          }
          resolve();
        }, 100);
      });
    });
  }

  /**
   * 跳转到开始时间
   */
  async jumpToStartTime() {
    const targetTime = this.context.data.leftBoundaryTime || 
                      TimeUtils.getCurrentTime() - 23 * 60 * 60 * 1000;
    
    await this.jumpToTime(targetTime);
    
    wx.showToast({
      title: "已跳转到最早时间",
      icon: "success",
      duration: 1500,
    });
  }

  /**
   * 回到最新时间
   */
  async backToLatest() {
    const currentTime = TimeUtils.getCurrentTime();
    await this.jumpToTime(currentTime);
    
    wx.showToast({
      title: "已回到最新时间",
      icon: "success",
      duration: 1500,
    });
  }

  /**
   * 处理拖拽开始
   */
  handleDragStart(e) {
    if (this.context.data.isRecordAreaDragging) return;
    
    this.stopAutoPlay();
    this.stopInertiaAnimation();
    
    if (this.context._videoPreviewTimer) {
      clearTimeout(this.context._videoPreviewTimer);
      this.context._videoPreviewTimer = null;
    }
    
    const touchX = e.touches[0].clientX;
    const currentTime = TimeUtils.getCurrentTime();
    
    this.initializeDragTracking();
    
    this.context.setData({
      isPlaying: false,
      isDragging: true,
      pausedTime: this.context.data.currentTime,
      dragStartX: touchX,
      dragStartTime: this.context.data.currentTime,
    });
    
    this.velocityTracker = {
      positions: [{ x: touchX, time: currentTime }],
      maxSamples: 5,
    };
  }

  /**
   * 处理拖拽移动
   */
  handleDragMove(e) {
    if (this.context.data.isRecordAreaDragging) return;
    
    const currentX = e.touches[0].clientX;
    const currentTime = TimeUtils.getCurrentTime();
    
    this.updateVelocityTracking(currentX, currentTime);
    
    const dragDistance = currentX - this.context.data.dragStartX;
    const timeOffset = (-dragDistance / this.context.data.pixelsPerSecond) * 1000;
    const newCurrentTime = this.context.data.dragStartTime + timeOffset;
    
    this.context._updateTimeForDrag(newCurrentTime);
  }

  /**
   * 处理拖拽结束
   */
  handleDragEnd() {
    this.context.setData({ isDragging: false });
    
    if (this.context._videoPreviewTimer) {
      clearTimeout(this.context._videoPreviewTimer);
      this.context._videoPreviewTimer = null;
    }
    
    const currentTime = this.context.data.currentTime;
    const realCurrentTime = TimeUtils.getCurrentTime();
    const { leftBoundaryTime } = this.context.data;
    
    if (currentTime > realCurrentTime) {
      this.handleTimeOverflow(realCurrentTime);
    } else if (leftBoundaryTime && currentTime < leftBoundaryTime) {
      this.handleTimeBoundary(leftBoundaryTime);
    } else {
      this.handleNormalDragEnd(currentTime);
    }
    
    this.cleanupDragState();
  }

  /**
   * 处理时间溢出
   */
  handleTimeOverflow(realCurrentTime) {
    const sliderValue = this.context._timeToSliderValue(realCurrentTime);
    
    this.context.setData({
      currentTime: realCurrentTime,
      baseTime: realCurrentTime,
      pausedTime: 0,
      sliderValue: sliderValue,
      isPlaying: true,
      formattedCurrentTime: TimeUtils.formatTime(realCurrentTime),
    });
    
    this.context.generateTimeScales();
    this.startAutoPlay();
  }

  /**
   * 处理时间边界
   */
  handleTimeBoundary(leftBoundaryTime) {
    const sliderValue = this.context._timeToSliderValue(leftBoundaryTime);
    
    this.context.setData({
      currentTime: leftBoundaryTime,
      baseTime: leftBoundaryTime,
      pausedTime: leftBoundaryTime,
      sliderValue: sliderValue,
      formattedCurrentTime: TimeUtils.formatTime(leftBoundaryTime),
    });
    
    this.context.generateTimeScales();
    
    wx.showToast({
      title: "已到达最早可查看时间",
      icon: "none",
      duration: 1500,
    });
  }

  /**
   * 处理正常拖拽结束
   */
  handleNormalDragEnd(currentTime) {
    this.context.setData({ pausedTime: currentTime });
    
    const velocity = this.calculateVelocity();
    
    if (Math.abs(velocity) > 0.3) {
      this.startInertiaAnimation(velocity);
    } else {
      this.context._updateVideoUrlAfterDrag(currentTime);
    }
  }

  /**
   * 初始化拖拽追踪
   */
  initializeDragTracking() {
    this.context._trackDragTracker = null;
    this.context._trackFormatCache = null;
    this.context._recordAreaUpdatePending = false;
    this.context._trackComplexUpdatePending = false;
  }

  /**
   * 更新速度追踪
   */
  updateVelocityTracking(currentX, currentTime) {
    if (this.velocityTracker) {
      this.velocityTracker.positions.push({ x: currentX, time: currentTime });
      
      if (this.velocityTracker.positions.length > this.velocityTracker.maxSamples) {
        this.velocityTracker.positions.shift();
      }
    }
  }

  /**
   * 计算拖拽速度
   */
  calculateVelocity() {
    if (!this.velocityTracker || this.velocityTracker.positions.length < 2) {
      return 0;
    }
    
    const positions = this.velocityTracker.positions;
    const latest = positions[positions.length - 1];
    const earliest = positions[0];
    
    const deltaX = latest.x - earliest.x;
    const deltaTime = latest.time - earliest.time;
    
    return deltaTime > 0 ? deltaX / deltaTime : 0;
  }

  /**
   * 开始惯性动画
   */
  startInertiaAnimation(initialVelocity) {
    this.stopInertiaAnimation();
    
    let velocity = initialVelocity;
    const startTime = TimeUtils.getCurrentTime();
    
    const animate = () => {
      velocity *= ANIMATION_CONSTANTS.INERTIA_FRICTION;
      
      if (Math.abs(velocity) < ANIMATION_CONSTANTS.MIN_VELOCITY) {
        this.stopInertiaAnimation();
        this.context._updateVideoUrlAfterDrag(this.context.data.currentTime);
        return;
      }
      
      const timeOffset = (-velocity * ANIMATION_CONSTANTS.FRAME_RATE_60) / this.context.data.pixelsPerSecond * 1000;
      const newTime = this.context.data.currentTime + timeOffset;
      
      this.context.setData({
        currentTime: newTime,
        baseTime: newTime,
        formattedCurrentTime: TimeUtils.formatTime(newTime),
      });
      
      this.context.generateTimeScales();
      
      this.inertiaAnimationId = requestAnimationFrame(animate);
    };
    
    this.inertiaAnimationId = requestAnimationFrame(animate);
  }

  /**
   * 停止惯性动画
   */
  stopInertiaAnimation() {
    if (this.inertiaAnimationId) {
      cancelAnimationFrame(this.inertiaAnimationId);
      this.inertiaAnimationId = null;
    }
  }

  /**
   * 清理拖拽状态
   */
  cleanupDragState() {
    if (this.velocityTracker) {
      this.velocityTracker = null;
    }
    
    if (this.context._trackDragTracker) {
      this.context._trackDragTracker = null;
    }
    
    if (this.context._dragTracker) {
      this.context._dragTracker = null;
    }
    
    this.context._trackFormatCache = null;
    this.context._cachedBaseData = null;
    this.context._recordAreaUpdatePending = false;
    this.context._trackComplexUpdatePending = false;
    this.context._formatUpdatePending = false;
  }
}
