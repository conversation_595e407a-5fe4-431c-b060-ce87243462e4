/**
 * 时间处理工具模块
 * 提供时间格式化、计算、对齐等功能
 */

const { TIME_CONSTANTS } = require('./constants.js');

class TimeUtils {
  /**
   * 格式化时间为显示字符串
   */
  static formatTime(timestamp) {
    if (!timestamp) return "";

    const date = new Date(timestamp);
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    const seconds = String(date.getSeconds()).padStart(2, "0");

    return `${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  /**
   * 格式化持续时间
   */
  static formatDuration(duration) {
    if (!duration || duration <= 0) return "0秒";

    const totalSeconds = Math.floor(duration / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}小时${minutes}分钟${seconds}秒`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 将时间对齐到指定间隔
   */
  static alignToInterval(timestamp, interval) {
    return Math.floor(timestamp / interval) * interval;
  }

  /**
   * 计算时间差
   */
  static getTimeDifference(startTime, endTime) {
    return Math.abs(endTime - startTime);
  }

  /**
   * 检查时间是否在范围内
   */
  static isTimeInRange(time, startTime, endTime) {
    return time >= startTime && time <= endTime;
  }

  /**
   * 获取当前真实时间
   */
  static getCurrentTime() {
    return Date.now();
  }

  /**
   * 计算时间到slider值的转换
   */
  static timeToSliderValue(time, minTime, maxTime, sliderMin = 0, sliderMax = 100) {
    if (maxTime <= minTime) return sliderMin;

    const ratio = (time - minTime) / (maxTime - minTime);
    return Math.max(sliderMin, Math.min(sliderMax, sliderMin + ratio * (sliderMax - sliderMin)));
  }

  /**
   * 计算slider值到时间的转换
   */
  static sliderValueToTime(value, minTime, maxTime, sliderMin = 0, sliderMax = 100) {
    if (sliderMax <= sliderMin) return minTime;

    const ratio = (value - sliderMin) / (sliderMax - sliderMin);
    return minTime + ratio * (maxTime - minTime);
  }

  /**
   * 获取时间边界
   */
  static getTimeBoundaries(currentTime) {
    const rightBoundary = currentTime;
    const leftBoundary = currentTime - TIME_CONSTANTS.HOURS_23_MS;

    return { leftBoundary, rightBoundary };
  }

  /**
   * 格式化刻度文本
   */
  static formatScaleText(date, scaleUnit) {
    switch (scaleUnit) {
      case "second":
        return `${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}:${String(date.getSeconds()).padStart(2, "0")}`;
      case "minute":
        return `${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
      case "10minute":
        return `${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
      default:
        return this.formatTime(date.getTime());
    }
  }

  /**
   * 检查是否为未来时间
   */
  static isFutureTime(timestamp) {
    return timestamp > this.getCurrentTime();
  }
}

module.exports = { TimeUtils };
