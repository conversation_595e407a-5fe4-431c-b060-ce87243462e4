/**
 * 常量定义模块
 * 定义播放器相关的常量配置
 */

export const SCALE_CONFIGS = [
  { unit: "second", text: "秒", interval: 1000 },
  { unit: "minute", text: "分钟", interval: 60000 },
  { unit: "10minute", text: "10分钟", interval: 600000 },
];

export const SCALE_LEVELS = {
  SECOND: 0,
  MINUTE: 1,
  TEN_MINUTE: 2,
};

export const TIME_CONSTANTS = {
  HOURS_24_MS: 24 * 60 * 60 * 1000,
  HOURS_23_MS: 23 * 60 * 60 * 1000,
  MINUTE_MS: 60 * 1000,
  SECOND_MS: 1000,
};

export const UI_CONSTANTS = {
  PIXELS_PER_INTERVAL: 10,
  BUFFER_ZONE_RATIO: 0.2,
  THROTTLE_DELAY: 100,
  SCALE_GENERATION_TIMEOUT: 5000,
};

export const ANIMATION_CONSTANTS = {
  FRAME_RATE_60: 16,
  FRAME_RATE_30: 33,
  INERTIA_FRICTION: 0.95,
  MIN_VELOCITY: 0.1,
};

export const RECORD_CONSTANTS = {
  MIN_DURATION_RATIO: 2,
  HANDLE_SIZE: 20,
  MIN_AREA_WIDTH: 40,
};
