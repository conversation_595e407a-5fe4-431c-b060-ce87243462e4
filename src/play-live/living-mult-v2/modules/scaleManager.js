/**
 * 刻度管理模块
 * 负责时间刻度的生成、更新和管理
 */

import { SCALE_CONFIGS, UI_CONSTANTS } from './constants.js';
import { TimeUtils } from './timeUtils.js';

export class ScaleManager {
  constructor(context) {
    this.context = context;
    this.isGenerating = false;
    this.lastGenerateTime = 0;
    this.generationPromise = null;
  }

  /**
   * 获取刻度配置
   */
  getScaleConfig(scaleUnit) {
    const configs = {
      second: { interval: 1000, showText: true, type: "major" },
      minute: { interval: 60000, showText: true, type: "major" },
      "10minute": { interval: 600000, showText: true, type: "major" },
    };
    
    return configs[scaleUnit] || configs.second;
  }

  /**
   * 获取刻度信息
   */
  getScaleInfo(date, scaleUnit) {
    switch (scaleUnit) {
      case "second":
        return {
          type: date.getSeconds() % 10 === 0 ? "major" : "minor",
          showText: date.getSeconds() % 10 === 0,
        };
      case "minute":
        return {
          type: date.getMinutes() % 5 === 0 ? "major" : "minor",
          showText: date.getMinutes() % 5 === 0,
        };
      case "10minute":
        return {
          type: "major",
          showText: true,
        };
      default:
        return { type: "major", showText: true };
    }
  }

  /**
   * 异步生成时间刻度
   */
  async generateTimeScalesAsync(forceScaleUnit) {
    if (this.isGenerating) {
      if (this.generationPromise) {
        return this.generationPromise;
      }
      
      this.generationPromise = this.waitForGeneration(forceScaleUnit);
      return this.generationPromise;
    }

    const { currentTime } = this.context.data;
    if (!forceScaleUnit && this.shouldSkipGeneration(currentTime)) {
      return;
    }

    this.isGenerating = true;
    this.lastGenerateTime = currentTime;

    try {
      await this.executeGeneration(forceScaleUnit);
    } catch (error) {
      console.error("异步刻度生成失败:", error);
      throw error;
    } finally {
      this.isGenerating = false;
    }
  }

  /**
   * 等待当前生成完成
   */
  async waitForGeneration(forceScaleUnit) {
    return new Promise((resolve, reject) => {
      const checkInterval = setInterval(() => {
        if (!this.isGenerating) {
          clearInterval(checkInterval);
          this.generationPromise = null;
          this.generateTimeScalesAsync(forceScaleUnit).then(resolve).catch(reject);
        }
      }, 10);
      
      setTimeout(() => {
        clearInterval(checkInterval);
        this.generationPromise = null;
        reject(new Error("刻度生成超时"));
      }, UI_CONSTANTS.SCALE_GENERATION_TIMEOUT);
    });
  }

  /**
   * 检查是否应该跳过生成
   */
  shouldSkipGeneration(currentTime) {
    return (
      this.lastGenerateTime &&
      Math.abs(currentTime - this.lastGenerateTime) < UI_CONSTANTS.THROTTLE_DELAY
    );
  }

  /**
   * 执行刻度生成
   */
  async executeGeneration(forceScaleUnit) {
    return new Promise((resolve) => {
      wx.nextTick(() => {
        this.generateTimeScalesSync(forceScaleUnit);
        resolve();
      });
    });
  }

  /**
   * 同步生成时间刻度
   */
  generateTimeScalesSync(forceScaleUnit) {
    const data = this.context.data;
    const scaleUnit = forceScaleUnit || data.scaleUnit;
    
    if (forceScaleUnit && forceScaleUnit !== data.scaleUnit) {
      console.log("刻度生成使用强制单位:", {
        forceScaleUnit,
        dataScaleUnit: data.scaleUnit,
        finalScaleUnit: scaleUnit
      });
    }

    if (!this.validateParameters(data)) {
      return;
    }

    const timeScales = this.generateScalePoints(data, scaleUnit);
    this.context.setData({ timeScales });
    this.context._updateRecordArea();
  }

  /**
   * 验证生成参数
   */
  validateParameters(data) {
    const { baseTime, pixelsPerSecond, containerWidth } = data;
    
    if (!baseTime || !pixelsPerSecond || !containerWidth || containerWidth <= 0) {
      console.warn("generateTimeScales: 参数无效", {
        baseTime, pixelsPerSecond, containerWidth
      });
      return false;
    }

    if (pixelsPerSecond <= 0) {
      console.warn("generateTimeScales: pixelsPerSecond 必须大于0");
      return false;
    }

    return true;
  }

  /**
   * 生成刻度点
   */
  generateScalePoints(data, scaleUnit) {
    const { baseTime, pixelsPerSecond, containerWidth, currentTime, timeRangeStart } = data;
    const scaleConfig = this.getScaleConfig(scaleUnit);
    
    const displayTimeRange = (containerWidth / pixelsPerSecond) * 1000;
    const halfRange = displayTimeRange / 2;
    const centerTime = currentTime || baseTime;
    
    const startTime = centerTime - halfRange;
    const endTime = centerTime + halfRange;
    
    let alignedStartTime = Math.floor(startTime / scaleConfig.interval) * scaleConfig.interval;
    const alignedEndTime = Math.ceil(endTime / scaleConfig.interval) * scaleConfig.interval;
    
    if (timeRangeStart && alignedStartTime < timeRangeStart) {
      alignedStartTime = Math.ceil(timeRangeStart / scaleConfig.interval) * scaleConfig.interval;
    }

    const bufferZone = containerWidth * UI_CONSTANTS.BUFFER_ZONE_RATIO;
    const visibleRange = this.calculateVisibleRange(centerTime, containerWidth, pixelsPerSecond, bufferZone);
    
    const virtualizedStartTime = Math.max(
      alignedStartTime,
      Math.floor(visibleRange.start / scaleConfig.interval) * scaleConfig.interval
    );
    const virtualizedEndTime = Math.min(
      alignedEndTime,
      Math.ceil(visibleRange.end / scaleConfig.interval) * scaleConfig.interval
    );

    return this.createScalePoints(
      virtualizedStartTime,
      virtualizedEndTime,
      scaleConfig,
      scaleUnit,
      centerTime,
      containerWidth,
      pixelsPerSecond,
      bufferZone
    );
  }

  /**
   * 计算可见范围
   */
  calculateVisibleRange(centerTime, containerWidth, pixelsPerSecond, bufferZone) {
    const visibleStartTime = centerTime - ((containerWidth / 2 + bufferZone) / pixelsPerSecond) * 1000;
    const visibleEndTime = centerTime + ((containerWidth / 2 + bufferZone) / pixelsPerSecond) * 1000;
    
    return { start: visibleStartTime, end: visibleEndTime };
  }

  /**
   * 创建刻度点数组
   */
  createScalePoints(startTime, endTime, scaleConfig, scaleUnit, centerTime, containerWidth, pixelsPerSecond, bufferZone) {
    const timeScales = [];
    const realCurrentTime = TimeUtils.getCurrentTime();
    
    for (let time = startTime; time <= endTime; time += scaleConfig.interval) {
      const date = new Date(time);
      const scaleInfo = this.getScaleInfo(date, scaleUnit);
      
      const centerPosition = containerWidth / 2;
      const relativeTime = (time - centerTime) / 1000;
      const position = centerPosition + relativeTime * pixelsPerSecond;
      
      if (position < -bufferZone || position > containerWidth + bufferZone) {
        continue;
      }
      
      const timeText = scaleInfo.showText ? TimeUtils.formatScaleText(date, scaleUnit) : "";
      const isFuture = TimeUtils.isFutureTime(time);
      
      timeScales.push({
        time,
        position,
        scaleType: scaleInfo.type,
        timeText,
        isFuture,
      });
    }
    
    return timeScales;
  }

  /**
   * 同步生成刻度（兼容旧接口）
   */
  generateTimeScales(forceRegenerate = false) {
    if (this.isGenerating) {
      return;
    }

    const { currentTime } = this.context.data;
    if (!forceRegenerate && this.shouldSkipGeneration(currentTime)) {
      return;
    }

    this.isGenerating = true;
    this.lastGenerateTime = currentTime;

    try {
      this.generateTimeScalesSync();
    } catch (error) {
      console.error("生成刻度异常:", error);
      this.context.setData({ timeScales: [] });
    } finally {
      this.isGenerating = false;
    }
  }
}
