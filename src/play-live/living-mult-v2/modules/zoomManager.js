/**
 * 缩放管理模块
 * 负责时间轴的缩放操作和刻度级别管理
 */

import { SCALE_CONFIGS, SCALE_LEVELS } from './constants.js';
import { TimeUtils } from './timeUtils.js';

export class ZoomManager {
  constructor(context, scaleManager) {
    this.context = context;
    this.scaleManager = scaleManager;
    this.isScaleSwitching = false;
    this.scaleOperationPromise = null;
  }

  /**
   * 放大刻度（提高时间精度）
   */
  async zoomIn() {
    if (this.context.data.isRecording) {
      return;
    }

    if (!this.context.data.canZoomIn) {
      wx.showToast({
        title: "已经是最精细的刻度了",
        icon: "none",
        duration: 1500,
      });
      return;
    }

    if (this.isScaleSwitching || this.scaleOperationPromise) {
      console.log("缩放操作进行中，忽略快速点击");
      return;
    }

    const currentLevel = this.context.data.scaleLevel;
    const newLevel = currentLevel - 1;
    const newConfig = SCALE_CONFIGS[newLevel];
    const pixelsPerSecond = 10000 / newConfig.interval;

    try {
      this.scaleOperationPromise = this.updateScaleAsync(newLevel, newConfig, pixelsPerSecond, "放大");
      await this.scaleOperationPromise;
      
      wx.showToast({
        title: `当前刻度已切换至 ${newConfig.text}`,
        icon: "none",
        duration: 1500,
      });
    } catch (error) {
      console.error("缩放操作失败:", error);
      wx.showToast({
        title: "缩放操作失败，请重试",
        icon: "none",
        duration: 1500,
      });
    } finally {
      this.scaleOperationPromise = null;
    }
  }

  /**
   * 缩小刻度（降低时间精度）
   */
  async zoomOut() {
    if (this.context.data.isRecording) {
      return;
    }

    if (!this.context.data.canZoomOut) {
      wx.showToast({
        title: "已经是最粗糙的刻度了",
        icon: "none",
        duration: 1500,
      });
      return;
    }

    if (this.isScaleSwitching || this.scaleOperationPromise) {
      console.log("缩放操作进行中，忽略快速点击");
      return;
    }

    const currentLevel = this.context.data.scaleLevel;
    const newLevel = currentLevel + 1;
    const newConfig = SCALE_CONFIGS[newLevel];
    const pixelsPerSecond = 10000 / newConfig.interval;

    try {
      this.scaleOperationPromise = this.updateScaleAsync(newLevel, newConfig, pixelsPerSecond, "缩小");
      await this.scaleOperationPromise;
      
      wx.showToast({
        title: `当前刻度已切换至 ${newConfig.text}`,
        icon: "none",
        duration: 1500,
      });
    } catch (error) {
      console.error("缩放操作失败:", error);
      wx.showToast({
        title: "缩放操作失败，请重试",
        icon: "none",
        duration: 1500,
      });
    } finally {
      this.scaleOperationPromise = null;
    }
  }

  /**
   * 异步更新刻度配置
   */
  async updateScaleAsync(newLevel, newConfig, pixelsPerSecond, action) {
    this.isScaleSwitching = true;
    
    try {
      this.updateScaleSync(newLevel, newConfig, pixelsPerSecond, action);
      await this.scaleManager.generateTimeScalesAsync(newConfig.unit);
      this.updateZoomButtonStatus();
    } catch (error) {
      console.error("刻度生成失败:", error);
      throw error;
    } finally {
      this.isScaleSwitching = false;
    }
  }

  /**
   * 同步更新刻度配置
   */
  updateScaleSync(newLevel, newConfig, pixelsPerSecond, action, shouldResetRecording, alignedRecordStartTime, alignedRecordEndTime) {
    const recordingParams = this.calculateRecordingParams(newConfig, shouldResetRecording, alignedRecordStartTime, alignedRecordEndTime);
    const timeParams = this.calculateTimeParams(newConfig);
    
    const updateData = {
      scaleLevel: newLevel,
      scaleUnit: newConfig.unit,
      scaleUnitText: newConfig.text,
      pixelsPerSecond: pixelsPerSecond,
      currentTime: timeParams.alignedCurrentTime,
      baseTime: timeParams.alignedBaseTime,
      pausedTime: timeParams.alignedCurrentTime,
      formattedCurrentTime: TimeUtils.formatTime(timeParams.alignedCurrentTime),
      trackOffset: 0,
      ...recordingParams.updateData
    };

    this.context.setData(updateData);
    this.handleRecordingDisplay(recordingParams);
  }

  /**
   * 计算录制相关参数
   */
  calculateRecordingParams(newConfig, shouldResetRecording, alignedRecordStartTime, alignedRecordEndTime) {
    if (shouldResetRecording === undefined) {
      const result = this.analyzeRecordingState(newConfig);
      shouldResetRecording = result.shouldReset;
      alignedRecordStartTime = result.alignedStartTime;
      alignedRecordEndTime = result.alignedEndTime;
    }

    const updateData = {};
    
    if (shouldResetRecording) {
      Object.assign(updateData, {
        isRecording: false,
        recordStartTime: 0,
        recordEndTime: 0,
        recordStartTimeFormatted: "",
        recordEndTimeFormatted: "",
        recordDurationText: "",
        recordAreaStyle: "display: none;",
      });
    } else if (this.context.data.isRecording) {
      Object.assign(updateData, {
        recordStartTime: alignedRecordStartTime,
        recordEndTime: alignedRecordEndTime,
        recordStartTimeFormatted: alignedRecordStartTime ? TimeUtils.formatTime(alignedRecordStartTime) : "",
        recordEndTimeFormatted: alignedRecordEndTime ? TimeUtils.formatTime(alignedRecordEndTime) : "",
      });
    }

    return { shouldResetRecording, alignedRecordStartTime, alignedRecordEndTime, updateData };
  }

  /**
   * 分析录制状态
   */
  analyzeRecordingState(newConfig) {
    const data = this.context.data;
    let shouldReset = false;
    let alignedStartTime = data.recordStartTime;
    let alignedEndTime = data.recordEndTime;

    if (data.isRecording && data.recordStartTime && data.recordEndTime) {
      const currentDuration = data.recordEndTime - data.recordStartTime;
      const minRequiredDuration = 2 * newConfig.interval;

      if (currentDuration < minRequiredDuration) {
        shouldReset = true;
      } else {
        alignedStartTime = TimeUtils.alignToInterval(data.recordStartTime, newConfig.interval);
        alignedEndTime = TimeUtils.alignToInterval(data.recordEndTime, newConfig.interval);

        if (alignedEndTime <= alignedStartTime) {
          alignedEndTime = alignedStartTime + newConfig.interval;
        }
      }
    }

    return { shouldReset, alignedStartTime, alignedEndTime };
  }

  /**
   * 计算时间相关参数
   */
  calculateTimeParams(newConfig) {
    const data = this.context.data;
    const currentTime = data.currentTime;
    const realCurrentTime = TimeUtils.getCurrentTime();
    const isAutoPlaying = data.isPlaying && !data.isDragging;

    let alignedCurrentTime, alignedBaseTime;

    if (isAutoPlaying && currentTime > realCurrentTime) {
      alignedCurrentTime = TimeUtils.alignToInterval(realCurrentTime, newConfig.interval);
      alignedBaseTime = TimeUtils.alignToInterval(realCurrentTime, newConfig.interval);
    } else {
      alignedCurrentTime = TimeUtils.alignToInterval(currentTime, newConfig.interval);
      alignedBaseTime = TimeUtils.alignToInterval(data.baseTime, newConfig.interval);
    }

    return { alignedCurrentTime, alignedBaseTime };
  }

  /**
   * 处理录制区域显示
   */
  handleRecordingDisplay(recordingParams) {
    const { shouldResetRecording } = recordingParams;
    
    if (shouldResetRecording) {
      return;
    }
    
    if (this.context.data.isRecording) {
      if (this.context._isRecordAreaPotentiallyVisible && this.context._isRecordAreaPotentiallyVisible()) {
        this.context._updateRecordArea && this.context._updateRecordArea(true);
        this.context._updateRecordDurationText && this.context._updateRecordDurationText();
      } else {
        this.context.setData({ recordAreaStyle: "display: none;" });
      }
    }
  }

  /**
   * 更新缩放按钮状态
   */
  updateZoomButtonStatus() {
    const { scaleLevel } = this.context.data;
    const maxLevel = SCALE_LEVELS.TEN_MINUTE;
    const minLevel = SCALE_LEVELS.SECOND;

    this.context.setData({
      canZoomIn: scaleLevel > minLevel,
      canZoomOut: scaleLevel < maxLevel,
    });
  }

  /**
   * 兼容旧版本的更新方法
   */
  _updateScale(newLevel, newConfig, pixelsPerSecond, action) {
    const recordingParams = this.calculateRecordingParams(newConfig);
    
    this.updateScaleSync(newLevel, newConfig, pixelsPerSecond, action, 
      recordingParams.shouldResetRecording, 
      recordingParams.alignedRecordStartTime, 
      recordingParams.alignedRecordEndTime);

    this.context.setData({}, () => {
      this.context.generateTimeScales(true);

      if (recordingParams.shouldResetRecording) {
        wx.showToast({
          title: "录制区域过短，请重新绘制",
          icon: "none",
          duration: 2000,
        });
      } else if (this.context.data.isRecording) {
        this.handleRecordingDisplay(recordingParams);
      }

      this.isScaleSwitching = false;
    });

    this.updateZoomButtonStatus();
  }
}
